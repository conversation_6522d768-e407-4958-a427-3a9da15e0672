package com.iflytek.edu.aicombination.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
@Configuration
public class ThreadPoolConfig {
    @Value("${ai.combination.core.pool.size}")
    protected Integer corePoolSize;

    @Value("${ai.combination.max.pool.size}")
    protected Integer maximumPoolSize;

    @Bean(name = " aiCombinationExecutor", destroyMethod = "shutdown")
    public ThreadPoolExecutor aiCombinationExecutor() {
        return new ThreadPoolExecutor(
                corePoolSize,  // 核心线程数  流式响应通常是 I/O 密集型，建议设为 2N ~ 5N（需结合业务压测调整,N 为 CPU 核心数）
                maximumPoolSize,  // 最大线程数 maximumPoolSize = 5N ~ 10N（结合队列容量）
            60L, // 空闲线程存活时间
            TimeUnit.SECONDS, // 时间单位
            // 有界队列
            new LinkedBlockingQueue<>(4096),
            // 自定义线程工厂
            new ThreadFactory() {
                private final AtomicInteger counter = new AtomicInteger(0);

                @Override
                public Thread newThread(Runnable r) {
                    return new Thread(r, "ai-combination-" + counter.incrementAndGet());
                }
            },
           // 当线程池满时调用者线程亲自执行任务
            new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }
}
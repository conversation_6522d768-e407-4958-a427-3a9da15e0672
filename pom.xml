<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.8</version>
    </parent>

    <groupId>com.iflytek.edu</groupId>
    <artifactId>ai-combination-paper</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <modules>
        <module>ai-combination-paper-dataapi</module>
        <module>ai-combination-paper-service</module>
        <module>ai-combination-paper-api</module>
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <revision>1.0.0</revision>
        <fastjson.version>2.0.53</fastjson.version>
    </properties>

    <repositories>
        <repository>
            <id>mvn-repo</id>
            <name>mvn-repo</name>
            <url>https://depend.iflytek.com/artifactory/mvn-repo/</url>
            <layout>default</layout>
        </repository>
    </repositories>


    <dependencies>
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
            <version>${fastjson.version}</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.0.2</version>
            </plugin>
        </plugins>

        <finalName>${project.artifactId}-${project.version}</finalName>
    </build>


    <!--指定依赖库新域名，skynet-framework升级后可删除-->
    <distributionManagement>
        <repository>
            <id>mvn-releases</id>
            <name>iflytek-nexus</name>
            <url>https://depend.iflytek.com/artifactory/ebg-mvn-release-private</url>
        </repository>
        <snapshotRepository>
            <id>mvn-snapshots</id>
            <name>iflytek-snapshots</name>
            <url>https://depend.iflytek.com/artifactory/ebg-mvn-snapshot-private</url>
        </snapshotRepository>
    </distributionManagement>
</project>

package com.iflytek.edu.aicombination;

import com.iflytek.skylab.core.dataapi.annotation.EnableFeatureAPI;
import com.iflytek.skyline.brave.annotation.EnableSkylineBrave;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.context.annotation.ImportResource;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import skynet.boot.annotation.EnableSkynetLogging;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AiCombinationPaperApplication
 * @description TODO
 * @date 2024/5/29 14:24
 */
@Slf4j
@EnablePaperDataAPI
@EnableSkynetLogging
@ImportResource({"classpath:applicationContext_dubbo.xml"})
@SpringBootApplication(exclude = {
        RedisAutoConfiguration.class
})
@EnableFeatureAPI
@EnableSkylineBrave
@EnableMongoRepositories(basePackages = "com.iflytek.edu.aicombination.mongo.dao")
public class AiCombinationPaperAppBoot {
    public static void main(String[] args) {
        SpringApplication.run(AiCombinationPaperAppBoot.class, args);
        log.info("================AI组卷服务启动成功============================");
    }
}
